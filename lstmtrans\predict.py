import time
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from torch.utils.data import Dataset

from model import FusionModel

class MyDataset(Dataset):
    def __init__(self, enc_in, enc_out):
        self.enc_in = torch.tensor(enc_in, dtype=torch.float32).unsqueeze(-1)
        self.enc_out = torch.tensor(enc_out, dtype=torch.float32).unsqueeze(-1)
        # self.enc_in = torch.from_numpy(enc_in).unsqueeze(-1)
        # self.dec_in = torch.from_numpy(dec_in).unsqueeze(-1)
        # self.dec_out = torch.from_numpy(dec_out).unsqueeze(-1)

    def __len__(self):
        return self.enc_in.shape[0]

    def __getitem__(self, idx):
        return self.enc_in[idx], self.enc_out[idx]

def MakeTestDataset(data, train_len, val_len):
    '''
    将test数据集按照滑动窗口切割为130长度的数据作为encoder的输入，decoder的输入和输出都为0向量
    :param data: 测试集，row代表站点，col代表时间
    :return: Tensor dataset
    '''
    window_size = train_len
    dec_size = val_len

    predict_enc = []
    predict_dec = []
    start_idxs = []
    for row_index in range(len(data)):
        # 获取当前站点数据
        row_data = data.iloc[row_index, :].values

        # 计算有效数据长度
        nonzero_indices = np.where(row_data != 0)       # [len, ]
        nonzero_indices = np.squeeze(nonzero_indices)   # [len]
        if len(nonzero_indices) == 0:
            continue
        valid_idx = nonzero_indices[-1]
        valid_len = valid_idx + 1
        if valid_len < window_size:
            continue
        # 存储有效数据的倒数第130位的index
        start_idxs.append(valid_idx - train_len)

        # 取最后130位数据作为encoder的输入
        enc_data = row_data[valid_idx - train_len:valid_idx]
        dec_data = np.zeros(dec_size, dtype=np.float32)
        # dec_data = torch.zeros(dec_size, dtype=torch.float32)    # dec_in size:[10]
        predict_enc.append(enc_data)
        predict_dec.append(dec_data)

    predict_enc = np.array(predict_enc).astype(float)
    predict_dec = np.array(predict_dec).astype(float)
    dec_dataset = MyDataset(enc_in=predict_enc, enc_out=predict_dec)

    # 测试训练集
    # test_set_path = r'./data/test_set.xlsx'
    # with pd.ExcelWriter(test_set_path) as writer:
    #     pd.DataFrame(predict_enc).to_excel(writer, sheet_name='predict_enc')
    #     pd.DataFrame(predict_dec).to_excel(writer, sheet_name='predict_dec')

    return dec_dataset, start_idxs


def test(model, data_loader, train_len):   # 测试集对130个encoder数据进行预测，直到预测长度达到260
    model.eval()
    with torch.no_grad():
        test_prediction = []
        for batch_idx, (enc_in, enc_out) in enumerate(data_loader):
            # enc_in: [batch, len, 1]   dec_in/out: [batch, len, 1]
            enc_in = enc_in.to(device)

            batch_predictions = enc_in.detach().cpu().numpy()
            pre_times = (max_len - enc_in.size(1)) // 10 + 1

            # 以10步长预测结果为一步，总共需要预测12次
            for _ in range(pre_times):
                # dec_in[:, 0, 0] = enc_in[:, -1, 0]
                out_put = model(enc_in)     # out_put: [batch, len]
                # dec_in[:, 0, 0] = out_put[:, 0]
                # for i in range(dec_in.size(1)):
                #     out_put = model(enc_in, dec_in)
                #     dec_in[:, i, 0] = out_put[:, i]
                # 每次根据130步长预测了10个数据后，将预测的结果添加到时序中去
                enc_opt = out_put.detach().cpu().unsqueeze(-1).numpy()
                batch_predictions = np.concatenate((batch_predictions, enc_opt), axis=1)
                # 更新最新的130个步长作为encoder的输入
                enc_in = torch.from_numpy(batch_predictions[:, -train_len:, :]).to(device)        # enc_in: [batch, len, 1]
                # 重置decoder的输入
                # enc_out = torch.zeros(dec_in.size(0), dec_in.size(1), 1).to(device)  # dec_in: [batch, len, 1]
                # 将预测的batch结果保存到数组中
            test_prediction.append(batch_predictions)
            # 动态拼接数组
            # max_len = max(pre.shape[1] for pre in test_prediction)
            # pad_res = []
            # for arr in test_prediction:
            #     # 计算需要填充的长度
            #     padding_len = max_len - arr.shape[1]
            #     padded_array = np.pad(arr, ((0, 0), (0, padding_len)), mode='constant')
            #     pad_res.append(padded_array)
            # prediction = np.concatenate(pad_res, axis=0)
        prediction = np.concatenate(test_prediction, axis=0)
    return prediction

if __name__ == '__main__':


    pre_file = r'./data/test.xlsx'
    save_file = rf'./data/opt{time.strftime("%Y_%m_%d_%H_%M", time.localtime(time.time()))}.xlsx'

    src_pad_idx = 0
    trg_pad_idx = 0
    enc_input_size = 130
    dec_input_size = 10
    d_model = 256
    max_len = 253
    n_head = 8
    ffn_hidden = 1024
    n_layers = 6
    dropout = 0.1
    device = torch.device('cuda')

    train_ratio = 0.9
    train_len = 200

    epochs = 100
    batch_size = 512

    # 测试集
    # test_data = pd.read_excel(pre_file, index_col='ID')
    test_data = pd.read_excel(pre_file, index_col='ID')
    test_dataset, start_idx = MakeTestDataset(data=test_data, train_len=enc_input_size, val_len=dec_input_size)
    test_loader = torch.utils.data.DataLoader(dataset=test_dataset, batch_size=batch_size, shuffle=False)



    # 定义模型
    model = FusionModel(in_dim=enc_input_size, out_dim=dec_input_size,
                        input_feature_dim=1, lstm_hidden=128, lstm_layers=2, transformer_dim=d_model,
                        nhead=n_head, num_transformer_layers=n_layers,
                        fc_hidden=ffn_hidden, output_feature_dim=1, dropout=dropout).cuda()

    # 设置损失函数
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

    epoch_train_loss = []
    epoch_val_loss = []


    # 加载模型并预测（调试版本）
    pt_path = r'./2025_05_27_15_10.pt'
    model.load_state_dict(torch.load(pt_path))

    predictions = test(model, test_loader, enc_input_size)     # [batch, len, 1]
    pre_timsries = []
    for i in range(len(predictions)):
        predict_values = predictions[i].reshape(-1)     # [batch, len, 1] >> [len, ]
        # start_idx存储着有效序列倒数第130的位置序列
        before_start_timsries = test_data.iloc[i][:start_idx[i]].values
        # 将130位以前的数据和最后130以后的预测数据拼接
        new_line = np.concatenate((before_start_timsries, predict_values), axis=0)
        pre_timsries.append(new_line)

        print(f'{i} / {len(predictions)} predict')

    new_df = pd.DataFrame(pre_timsries, index=test_data.index)
    new_df.to_excel(save_file)