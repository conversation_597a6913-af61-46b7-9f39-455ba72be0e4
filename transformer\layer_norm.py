import torch
import torch.nn as nn

class LayerNorm(nn.Module):
    def __init__(self, d_model, eps=1e-6):
        super(LayerNorm, self).__init__()
        # nn.Parameter将不可训练的tensor转换为可训练类型的parameter
        self.gamma = nn.Parameter(torch.ones(d_model))
        self.beta = nn.Parameter(torch.zeros(d_model))
        self.eps = eps

    def forward(self, x):
        # tensor: [batch, length, d_model]
        # If keepdim is True, the output tensor is of the same size as input except in the dimension(s) dim where it is of size 1
        mean = x.mean(-1, keepdim=True)
        variance = x.var(-1, unbiased=False, keepdim=True)
        # -1 means last dimension

        out = (x - mean) / torch.sqrt((variance + self.eps))

        # out = w * x + b, out也是可学习参数
        out = self.gamma * out + self.beta
        return out