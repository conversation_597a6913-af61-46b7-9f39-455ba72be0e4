import torch
import torch.nn as nn
import numpy as np

class FusionModel(nn.Module):
    def __init__(self, in_dim=130, out_dim=10,input_feature_dim=1, lstm_hidden=128, lstm_layers=2, transformer_dim=256, nhead=8,
                 num_transformer_layers=2, fc_hidden=32, output_feature_dim=1, dropout=0.1):
        super(FusionModel, self).__init__()

        # 输出维度
        self.pre_len = out_dim

        # 线性层改变长度
        self.len_linear = nn.Linear(in_dim, out_dim)

        # LSTM层：提取局部时序特征 embedding1
        # lstm(input_dim=1, hidden_dim=512, lstm_layers=2): [batch, len, input_dim] >> [batch, len, hidden_dim]
        # batch_first=Ture: [batch, len, hidden_dim] >> False:[len, batch, hidden_dim]
        self.lstm = nn.LSTM(input_feature_dim, lstm_hidden, lstm_layers, batch_first=True)

        # LSTM映射到transformer输入的维度 embedding2
        self.linear_before_transformer = nn.Linear(lstm_hidden, transformer_dim)

        # 位置编码
        self.pos_encoder = PositionalEncoding(transformer_dim)

        # transformer encoder层，捕捉全局依赖关系
        encoder_layer = nn.TransformerEncoderLayer(d_model=transformer_dim, nhead=nhead, dropout=dropout)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_transformer_layers)

        # 后续全连接层
        self.fc = nn.Linear(transformer_dim, out_dim)

        self.linear_after_transformer = nn.Linear(transformer_dim, fc_hidden)
        self.fc_out = nn.Linear(fc_hidden, output_feature_dim)
        self.in2out_dim = nn.Linear(in_dim, out_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        '''
        x: [batch_size, seq_length, input_dim]
        '''

        # 对齐len: [batch, input_len, 1] >> [batch, output_len, 1], 以下的len都是out_len
        # x = self.len_linear(x.squeeze(-1)).unsqueeze(-1)

        # LSTM layer output
        lstm_out, _ = self.lstm(x)  # output_shape: [batch, len, 1] >> [batch, len, lstm_hidden]
        lstm_out = self.linear_before_transformer(lstm_out)     # [batch, len, lstm_hidden] >> [batch, len, trans_dim]

        # 位置编码
        trans_in = self.pos_encoder(lstm_out)   # [batch, len, trans_dim]

        # 映射到Transformer 维度
        # transformer_input = self.linear_before_transformer(trans_in)  # shape: [batch, len, trans_dim] >> [batch, len, hidden_dim]

        # Transformer需要 [seq, batch, d_model]
        # if len(transformer_input.shape) < 3:
        #     transformer_input = transformer_input.unsqueeze(0)
        # transformer_input = transformer_input.transpose(0, 1)
        transformer_output = self.transformer_encoder(trans_in)    # [batch, len, trans_dim]
        # transformer_output = transformer_output.transpose(0, 1)  # [batch, seq, transformer]

        # 取最后一个时间步的输出作为整个序列的表示
        fc = self.dropout(self.linear_after_transformer(transformer_output))    # [batch, len, trans_dim] >> [batch, len, fc_hidden]
        fc = self.dropout(self.fc_out(fc))  # [batch, len, fc_hidden] >> [batch, len, output_dim]
        final_feature = fc[:, -self.pre_len:, :]  # [batch, len, output_dim] >> [batch, pre_len, output_dim]
        fc_out = final_feature.squeeze(-1)      # [batch, pre_len, output_dim] >> [batch, pre_len]
        # final_feature = transformer_output[:, -1, :]    # [batch, len, trans_dim] >> [batch, trans_dim]
        # last_output = transformer_output[:, -self.step:, :]  # [batch, transformer_dim]
        # fc = self.dropout(self.linear_after_transformer(last_output))
        # fc = self.dropout(self.linear_after_transformer(transformer_output))    # [batch, len, fc_hidden]

        # fc = self.dropout(self.fc(final_feature))             # [batch, out_len]
        # print('0')

        # fc_out = self.fc_out(fc)   # [batch, len, 1]
        # fc_out = fc_out.squeeze(-1)  # [batch, len, 1] >> [batch, len]
        # out = self.in2out_dim(fc_out)       # [batch, in_len] >> [batch, out_len]
        return fc_out


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.encoding = torch.zeros(max_len, d_model).cuda()
        self.encoding.requires_grad = False
        pos = torch.arange(0, max_len).float().unsqueeze(dim=1)
        i_even = torch.arange(0, 2 * max_len, step=2).float().unsqueeze(dim=1)

        # 奇偶数位置
        self.encoding[:, 0::2] = torch.sin(pos / (10000 ** (i_even / d_model)))
        # 奇数位置
        self.encoding[:, 1::2] = torch.cos(pos / (10000 ** (i_even / d_model)))
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # [batch, len, d_model]
        # xc = x.squeeze(-1)
        batch_size, seq_len, _ = x.size()
        out = x + self.encoding[:seq_len, :].unsqueeze(0)        # [seq, d_model] >> [1, seq, d_model]
        return self.dropout(out)