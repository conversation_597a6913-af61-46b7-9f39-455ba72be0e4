import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class moving_avg(nn.Module):
    """
    Moving average block to highlight the trend of time series
    """

    def __init__(self, kernel_size, stride):
        super(moving_avg, self).__init__()
        self.kernel_size = kernel_size
        self.avg = nn.AvgPool1d(kernel_size=kernel_size, stride=stride, padding=0)

    def forward(self, x):
        rep_f = (self.kernel_size - 1) // 2 if self.kernel_size % 2 != 0 else int(self.kernel_size / 2)
        rep_e = (self.kernel_size - 1) // 2 if self.kernel_size % 2 != 0 else int(self.kernel_size / 2 - 1)

        # padding on the both ends of time series
        front = x[:, 0:1, :].repeat(1, rep_f, 1)
        end = x[:, -1:, :].repeat(1, rep_e, 1)

        x = torch.cat([front, x, end], dim=1)
        x = self.avg(x.permute(0, 2, 1))
        x = x.permute(0, 2, 1)

        return x


class series_decomp(nn.Module):
    """
    Series decomposition block
    """

    def __init__(self, kernel_size):
        super(series_decomp, self).__init__()
        self.moving_avg = moving_avg(kernel_size, stride=1)

    def forward(self, x):
        moving_mean = self.moving_avg(x)
        res = x - moving_mean
        return res, moving_mean


class Model(nn.Module):
    """
    Decomposition-Linear
    """

    def __init__(self, train_len, pre_len):
        super(Model, self).__init__()

        self.seq_len = train_len
        self.pred_len = pre_len

        # Decompsition Kernel Size
        kernel_size = 24
        self.decompsition = series_decomp(kernel_size)

        self.Linear_Seasonal = nn.Linear(self.seq_len, self.pred_len)
        self.Linear_Trend = nn.Linear(self.seq_len, self.pred_len)

        self.LSTM_Seasonal = nn.LSTM(self.seq_len, self.pred_len)
        self.LSTM_Trend = nn.LSTM(self.seq_len, self.pred_len)

    def forward(self, x):
        # x: [Batch, Input length, Channel]
        seasonal_init, trend_init = self.decompsition(x)
        seasonal_init, trend_init = seasonal_init.permute(0, 2, 1), trend_init.permute(0, 2, 1)


        # seasonal_output = self.Linear_Seasonal(seasonal_init)
        # trend_output = self.Linear_Trend(trend_init)
        seasonal_output, _ = self.LSTM_Seasonal(seasonal_init)
        trend_output, _ = self.LSTM_Trend(trend_init)

        x = seasonal_output + trend_output
        return x.permute(0, 2, 1)  # to [Batch, Output length, Channel]