import torch
import torch.nn as nn
from Embed import positional_encoding

class PatchTST_Model(nn.Module):
    def __init__(self, seq_len=130, out_len=10, patch_len=10, stride=10, d_model=512,
                 pe='zeros', learn_pe=True, nhead=8, num_layer=4, dropout=0.1):
        super(PatchTST_Model, self).__init__()

        # patching
        self.patch_len = patch_len
        self.stride = stride
        self.seq_len = seq_len
        self.patch_num = int((seq_len - patch_len) / stride + 1)

        # projection + learnable positional patching
        self.d_model = d_model
        self.W_P = nn.Linear(self.patch_len, self.d_model)
        self.W_pos = positional_encoding(pe, learn_pe, self.patch_num, self.d_model)
        self.embed_dropout = nn.Dropout(dropout)

        # transformer
        encoder_layer = nn.TransformerEncoderLayer(d_model=self.d_model, nhead=nhead, dropout=dropout)
        self.trans_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layer)

        # flatten + Linear Head
        self.out_len = out_len
        self.flatten = nn.Flatten(start_dim=-2)
        self.f_linear = nn.Linear(self.d_model * self.patch_num, self.out_len)
        self.f_dropout = nn.Dropout(dropout)

    def forward(self, x):
        # x: [batch, seq_len, input_dim] > [batch, in_len, 1]
        input_dim = x.size(-1)

        # Patching
        x = x.permute(0, 2, 1)                                                  # x: [batch, input_dim, in_len]
        x = x.unfold(dimension=-1, size=self.patch_len, step=self.stride)     # x: [batch, input_dim, patch_num, patch_len]

        # projection + positional embedding
        x = self.W_P(x)                                                         # x: [batch, input_dim, patch_num, d_model]
        u = torch.reshape(x, (x.shape[0]*x.shape[1], x.shape[2], x.shape[3]))   # u: [batch * input_dim, patch_num, d_model]
        u = self.embed_dropout(u + self.W_pos)                                  # u: [batch * input_dim, patch_num, d_model]

        # transformer
        x = self.trans_encoder(u)                                               # x: [batch * input_dim, patch_num, d_model]
        x = torch.reshape(x, (-1, input_dim, x.shape[1], x.shape[2]))           # x: [batch, input_dim, patch_num, d_model]

        # flatten
        x = self.flatten(x)                                                     # x: [batch, input_dim, patch_num * d_model]
        x = self.f_linear(x)                                                    # x: [batch, input_dim, out_len]
        x = x.permute(0, 2, 1)                                                  # x: [batch, out_len, input_dim]
        x = self.f_dropout(x)

        return x
