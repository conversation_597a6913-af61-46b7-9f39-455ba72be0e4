import torch
from torch import nn
import math


# Embedding 将输入的词汇/序列转换为指定维度的Embedding
class TokenEmbedding(nn.Embedding):
    """
    Token Embedding using torch.nn
    they will dense representation of word using weighted matrix
    """
    # input_size: 输入长度
    # d_model: 字向量维度,如果输入的是时序预测，则字向量的维度可以等于1
    def __init__(self, input_size, d_model):
        super(TokenEmbedding, self).__init__(input_size, d_model, padding_idx=1)

# 用全链接层或者LSTM代替Embedding


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len, device):
        super(PositionalEncoding, self).__init__()
        self.encoding = torch.zeros(max_len, d_model, device=device)
        self.encoding.requires_grad = False
        pos = torch.arange(0, max_len, device=device).float().unsqueeze(dim=1)
        i_even = torch.arange(0, 2 * max_len, step=2, device=device).float().unsqueeze(dim=1)

        # 奇偶数位置
        self.encoding[:, 0::2] = torch.sin(pos / (10000 ** (i_even/d_model)))
        # 奇数位置
        self.encoding[:, 1::2] = torch.cos(pos / (10000 ** (i_even/d_model)))

    def forward(self, x):
        # [batch, len, 1] >> [batch, len]
        xc = x.squeeze(-1)
        batch_size, seq_len = xc.size()
        out = self.encoding[:seq_len, :].clone()   # [seq, d_model]
        return out

class TransformerEmbedding(nn.Module):
    '''
    token embedding + positional encoding
    '''
    def __init__(self, input_size, d_model, max_len, dropout, device):
        super(TransformerEmbedding, self).__init__()
        self.token_embeddings = TokenEmbedding(input_size, d_model)
        # lstm(input_dim=1, hidden_dim=512, lstm_layers=2): [batch, len, input_dim] >> [batch, len, d_model]
        self.lstm = nn.LSTM(1, d_model, 2, batch_first=True)        # [batch, len, d_model]
        self.linear_before_transform = nn.Linear(d_model, d_model)   # [batch, len, d_model] >> [batch, len, d_model]
        self.linear = nn.Linear(1, d_model)
        self.position_embeddings = PositionalEncoding(d_model, max_len, device)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, coder):
        # x: [batch, len] >> [batch, len, d_model]
        # token_embeddings = self.token_embeddings(x)     # embedding function input must in cpu
        x = x.view(x.size(0), x.size(1), -1)      # [batch, len] >> [batch, len, 1]
        # print(f'{x._version}')
        if coder == 'encoder':
            lstm_out, (_, _) = self.lstm(x)
            linear_embeddings = self.linear_before_transform(lstm_out)
        elif coder == 'decoder':
            # linear_embeddings = torch.nn.functional.linear(x, self.linear.weight.clone(), self.linear.bias)
            linear_embeddings = self.linear(x).clone()      # [batch, len, 1] >> [batch, len, d_model]
            # print(f'{x._version}')
        position_embeddings = self.position_embeddings(x)
        # print(f'{position_embeddings._version}')
        out = self.dropout(linear_embeddings + position_embeddings)
        return out