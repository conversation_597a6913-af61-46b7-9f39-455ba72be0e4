import random
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from torch.utils.data import Dataset

# from models.transformer.transformer import Transformer
from transformer_layer import Transformer

class MyDataset(Dataset):
    def __init__(self, enc_in, dec_in, dec_out):
        self.enc_in = torch.tensor(enc_in, dtype=torch.float32).unsqueeze(-1)
        self.dec_in = torch.tensor(dec_in, dtype=torch.float32).unsqueeze(-1)
        self.dec_out = torch.tensor(dec_out, dtype=torch.float32).unsqueeze(-1)
        # self.enc_in = torch.from_numpy(enc_in).unsqueeze(-1)
        # self.dec_in = torch.from_numpy(dec_in).unsqueeze(-1)
        # self.dec_out = torch.from_numpy(dec_out).unsqueeze(-1)

    def __len__(self):
        return self.enc_in.shape[0]

    def __getitem__(self, idx):
        return self.enc_in[idx], self.dec_in[idx], self.dec_out[idx]

def MakeData(data, train_ratio, train_len):
    # 将前200个词作为enc_input, 将200以后的词作为dec_input， 采用teacher forcing策略dec_input在训练时输入的是真值
    # 在验证集预测时dec_input的输入为等长的向量1
    # data: [stations, values]

    # 数据标准化 >> scaled_data: [values, stations]
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(np.transpose(data))
    scaled_data = np.transpose(scaled_data)     # [values, stations] >> [stations, values]

    # 随机打乱数据集的stations维度
    random.shuffle(scaled_data)

    # 取前200训练，200以后预测
    trains, vals = scaled_data[:, :train_len], scaled_data[:, train_len:]

    # 取前90%作为训练集，后10%作为验证集
    train_size = int(len(data) * train_ratio)
    xs_train = trains[:train_size, :]
    xs_test = trains[train_size:, :]
    ys_train = vals[:train_size, :]
    ys_test = vals[train_size:, :]

    dec_ones = torch.ones(ys_test.shape[0], ys_test.shape[1], dtype=torch.float32)

    train_dataset = MyDataset(enc_in=xs_train, dec_in=ys_train, dec_out=ys_train)
    test_dataset = MyDataset(enc_in=xs_test, dec_in=dec_ones, dec_out=ys_test)

    return train_dataset, test_dataset

def MakeData_SW(data, train_ratio):
    '''
    滑动窗口构建数据集，enc_in的长度为130，dec_in和dec_out的长度为10.在训练集之中dec_in和dec_out都为长度为10的0向量
    :param data: 输入时序数据 [stations, values]
    :param train_ratio: 训练集和验证集的比例
    :param train_len: 训练的总长度
    :return: 训练集和验证集
    '''

    window_size = 140   # enc_in size 130; dec_in size 10
    train_size = int(len(data) * train_ratio)   # 训练集的长度

    xs_train, xs_test, ys_train, ys_test, window_data = [], [], [], [], []

    # 数据标准化 >> scaled_data: [values, stations]
    scaler = MinMaxScaler(feature_range=(0, 1))
    # scaled_data = scaler.fit_transform(np.transpose(data))
    # scaled_data = np.transpose(scaled_data)     # [values, stations] >> [stations, values]

    # 随机打乱数据集的stations维度
    # random.shuffle(scaled_data)
    data = np.array(data)
    random.shuffle(data)


    # 遍历每一个气象站点数据
    for row_idx in range(len(data)):
        # 获取当前的站点时序数据
        row_data = data[row_idx, :]

        # 计算有效数据长度（找到最后一个非零元素的索引）
        # nonzero_indices = np.where(row_data != 0)
        nonzero_indices = np.where(row_data != 0)       # [len, ]
        nonzero_indices = np.squeeze(nonzero_indices)   # [len]
        if len(nonzero_indices) == 0:
            continue    # 跳过全零的行
        valid_len = nonzero_indices[-1] + 1 # 有效数据长度

        # 检查有效数据是否足够生成窗口：
        if valid_len < window_size:
            continue

        # 生成滑动窗口
        num_windows = valid_len - window_size + 1
        for start_idx in range(num_windows):
            # 滑动窗尽口截取数据
            end_idx = start_idx + window_size
            window = row_data[start_idx:end_idx].reshape(1, -1)     # [140, ] >> [1, 140]

            # 训练数据归一化
            scaled_window = scaler.fit_transform(np.transpose(window))
            scaled_window = np.transpose(scaled_window)

            window_data.append(scaled_window)

    window_data = np.array(window_data).squeeze(1)      # list to array, [rows, 1, len] >>[rows, len]
    xs_train = np.array(window_data[:train_size, :130])
    ys_train = np.array(window_data[:train_size, 130:])
    xs_test = np.array(window_data[train_size:, :130])
    ys_test = np.array(window_data[train_size:, 130:])

    # 测试时的decoder输入为0向量
    dec_zeros = torch.zeros(ys_test.shape[0], ys_test.shape[1], dtype=torch.float32).squeeze(-1)


    train_dataset = MyDataset(enc_in=xs_train, dec_in=ys_train, dec_out=ys_train)
    test_dataset = MyDataset(enc_in=xs_test, dec_in=dec_zeros, dec_out=ys_test)

    return train_dataset, test_dataset

def MakeTestDataset(data):
    '''
    将test数据集按照滑动窗口切割为130长度的数据作为encoder的输入，decoder的输入和输出都为0向量
    :param data: 测试集，row代表站点，col代表时间
    :return: Tensor dataset
    '''
    window_size = 130
    dec_size = 10

    predict_enc = []
    predict_dec = []
    start_idxs = []
    for row_index in range(len(data)):
        # 获取当前站点数据
        row_data = data.iloc[row_index, :].values

        # 计算有效数据长度
        nonzero_indices = np.where(row_data != 0)       # [len, ]
        nonzero_indices = np.squeeze(nonzero_indices)   # [len]
        if len(nonzero_indices) == 0:
            continue
        valid_idx = nonzero_indices[-1]
        valid_len = valid_idx + 1
        if valid_len < window_size:
            continue
        # 存储有效数据的倒数第130位的index
        start_idxs.append(valid_idx - 130)

        # 取最后130位数据作为encoder的输入
        enc_data = row_data[valid_idx - 130:valid_idx]
        dec_data = np.zeros(dec_size, dtype=np.float32)
        # dec_data = torch.zeros(dec_size, dtype=torch.float32)    # dec_in size:[10]
        predict_enc.append(enc_data)
        predict_dec.append(dec_data)

    predict_enc = np.array(predict_enc).astype(float)
    predict_dec = np.array(predict_dec).astype(float)
    dec_dataset = MyDataset(enc_in=predict_enc, dec_in=predict_dec, dec_out=predict_dec)

    return dec_dataset, start_idxs


def train(model, data_loader, optimizer, criterion):
    model.train()
    epoch_train_loss = 0
    # with torch.autograd.detect_anomaly():
    for batch_idx, (enc_in, dec_in, dec_out) in enumerate(data_loader):
        enc_in, dec_in, dec_out = enc_in.to(device), dec_in.to(device), dec_out.to(device).squeeze(-1)  # [batch, len, 1] >> [batch, len]

        optimizer.zero_grad()
        output = model(enc_in, dec_in)
        loss = criterion(output, dec_out)
        loss.backward(retain_graph=False)
        optimizer.step()
        epoch_train_loss += loss.item() * enc_in.size(0)
    epoch_train_loss /= len(data_loader)
    return epoch_train_loss

def val(model, data_loader, criterion):
    model.eval()
    epoch_test_loss = 0
    with torch.no_grad():
        for batch_idx, (enc_in, dec_in, dec_out) in enumerate(data_loader):
            # enc_in: [batch, len, 1]   dec_in/out: [batch, len, 1]
            enc_in, dec_in, dec_out = enc_in.to(device), dec_in.to(device), dec_out.to(device)

            # 将enc_in的最后一个当作dec_in的第一个输入
            dec_in[:, 0] = enc_in[:, -1]

            # 将预测值传递给dec_in作为第一个输入
            out_put = model(enc_in, dec_in)     # out_put: [batch, len]
            dec_in[:, 0, 0] = out_put[:, 0]
            # output_reshape = out_put.contiguous().view(-1, out_put.size(-1))


            # 253 - 130 = 123个预测
            # for i in range(1, max_len - enc_input_size):
            # 用130步去预测10步
            for i in range(dec_in.size(1)):

                out_put = model(enc_in, dec_in)
                # # 将最新的一步预测拼接到之前的预测之中
                # out_put = torch.cat((out_put, output_step[:, -1:]), dim=-1)
                # 预测的最新值传递给dec_in
                dec_in[:, i, 0] = out_put[:, i]

            loss = criterion(dec_in, dec_out)
            epoch_test_loss += loss.item() * enc_in.size(0)

    epoch_test_loss /= len(data_loader)
    return epoch_test_loss

def test(model, data_loader):   # 测试集对130个encoder数据进行预测，直到预测长度达到260
    model.eval()
    with torch.no_grad():
        test_prediction = []
        for batch_idx, (enc_in, dec_in, dec_out) in enumerate(data_loader):
            # enc_in: [batch, len, 1]   dec_in/out: [batch, len, 1]
            enc_in, dec_in = enc_in.to(device), dec_in.to(device)

            batch_predictions = enc_in.detach().cpu().numpy()
            pre_times = (max_len - enc_in.size(1)) // 10 + 1

            # 以10步长预测结果为一步，总共需要预测12次
            for _ in range(pre_times):
                dec_in[:, 0, 0] = enc_in[:, -1, 0]
                out_put = model(enc_in, dec_in)     # out_put: [batch, len]
                dec_in[:, 0, 0] = out_put[:, 0]
                for i in range(dec_in.size(1)):
                    out_put = model(enc_in, dec_in)
                    dec_in[:, i, 0] = out_put[:, i]
                # 每次根据130步长预测了10个数据后，将预测的结果添加到时序中去
                dec_res = dec_in.detach().cpu().numpy()
                batch_predictions = np.concatenate((batch_predictions, dec_res), axis=1)
                # 更新最新的130个步长作为encoder的输入
                enc_in = torch.from_numpy(batch_predictions[:, -130:, :]).to(device)        # enc_in: [batch, len, 1]
                # 重置decoder的输入
                dec_in = torch.zeros(dec_in.size(0), dec_in.size(1), 1).to(device)  # dec_in: [batch, len, 1]
                # 将预测的batch结果保存到数组中
            test_prediction.append(batch_predictions)
            # 动态拼接数组
            # max_len = max(pre.shape[1] for pre in test_prediction)
            # pad_res = []
            # for arr in test_prediction:
            #     # 计算需要填充的长度
            #     padding_len = max_len - arr.shape[1]
            #     padded_array = np.pad(arr, ((0, 0), (0, padding_len)), mode='constant')
            #     pad_res.append(padded_array)
            # prediction = np.concatenate(pad_res, axis=0)
        prediction = np.concatenate(test_prediction, axis=0)
    return prediction

if __name__ == '__main__':

    train_file = r'./data/train.xlsx'
    pre_file = r'./data/test.xlsx'
    save_file = r'./data/Transformer_pre.xlsx'

    src_pad_idx = 0
    trg_pad_idx = 0
    enc_input_size = 130
    dec_input_size = 10
    d_model = 512
    max_len = 253
    n_head = 8
    ffn_hidden = 1024
    n_layers = 6
    dropout = 0.1
    device = torch.device('cuda')

    train_ratio = 0.9
    train_len = 200

    epochs = 200
    batch_size = 256

    # 制作数据集
    # 训练集和验证集

    train_data = pd.read_excel(train_file, index_col='ID')
    train_dataset, test_dataset = MakeData_SW(data=train_data, train_ratio=train_ratio)
    train_loader = torch.utils.data.DataLoader(dataset=train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = torch.utils.data.DataLoader(dataset=test_dataset, batch_size=batch_size, shuffle=True)

    # 测试集
    # test_data = pd.read_excel(pre_file, index_col='ID')
    test_data = pd.read_excel(pre_file, index_col='ID')
    test_dataset, start_idx = MakeTestDataset(data=test_data)
    test_loader = torch.utils.data.DataLoader(dataset=test_dataset, batch_size=batch_size, shuffle=False)

    # 定义模型
    model = Transformer(src_pad_idx, trg_pad_idx, enc_input_size, dec_input_size, d_model, max_len, n_head,
                        ffn_hidden, n_layers, dropout, device).cuda()

    # 设置损失函数
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

    epoch_train_loss = []
    epoch_val_loss = []


    for epoch in range(epochs):
        train_loss = train(model, train_loader, optimizer, criterion)
        epoch_train_loss.append(train_loss)

        val_loss = val(model, val_loader, criterion)
        epoch_val_loss.append(val_loss)

        # if epoch % 5 == 0:
        #     print(f'Epoch: {epoch + 1}/{epochs}, train_loss: {train_loss:.4f}, val_loss: {val_loss:.4f}')
        print(f'Epoch: {epoch + 1}/{epochs}, train_loss: {train_loss:.4f}, val_loss: {val_loss:.4f}')

    torch.save(model.state_dict(), r'./transformer_model.pt')

    with open('loss.txt', 'a') as f:
        f.write(str('train loss: ' + str(epoch_train_loss[-1]) + 'val loss: ' + str(epoch_val_loss[-1]) + '\n'))



    # 加载模型并预测（调试版本）
    # model.load_state_dict(torch.load(r'E:\LSTM\model\transformer_model.pt'))

    predictions = test(model, test_loader)      # [batch, len, 1]
    pre_timsries = []
    for i in range(len(predictions)):
        predict_values = predictions[i].reshape(-1)     # [batch, len, 1] >> [len, ]
        # start_idx存储着有效序列倒数第130的位置序列
        before_start_timsries = test_data.iloc[i][:start_idx[i]].values
        # 将130位以前的数据和最后130以后的预测数据拼接
        new_line = np.concatenate((before_start_timsries, predict_values), axis=0)
        pre_timsries.append(new_line)

        print(f'{i} / {len(predictions)} predict')

    new_df = pd.DataFrame(pre_timsries, index=test_data.index)
    new_df.to_excel(save_file)

