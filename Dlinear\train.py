import random
import time
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from torch.utils.data import Dataset

from DLinear import Model

class MyDataset(Dataset):
    def __init__(self, enc_in, enc_out):
        self.enc_in = torch.tensor(enc_in, dtype=torch.float32).unsqueeze(-1)
        self.enc_out = torch.tensor(enc_out, dtype=torch.float32).unsqueeze(-1)
        # self.enc_in = torch.from_numpy(enc_in).unsqueeze(-1)
        # self.dec_in = torch.from_numpy(dec_in).unsqueeze(-1)
        # self.dec_out = torch.from_numpy(dec_out).unsqueeze(-1)

    def __len__(self):
        return self.enc_in.shape[0]

    def __getitem__(self, idx):
        return self.enc_in[idx], self.enc_out[idx]


def MakeData_SW(data, train_ratio, train_len, val_len):
    '''
    滑动窗口构建数据集，enc_in的长度为130，dec_in和dec_out的长度为10.在训练集之中dec_in和dec_out都为长度为10的0向量
    :param data: 输入时序数据 [stations, values]
    :param train_ratio: 训练集和验证集的比例
    :param train_len: 窗口的训练长度， val_len:窗口的验证长度
    :return: 训练集和验证集
    '''

    # window_size = 140
    window_size = train_len + val_len    # enc_in size 130; dec_in size 10
    train_size = int(len(data) * train_ratio)   # 训练集的长度

    xs_train, xs_test, ys_train, ys_test, window_data = [], [], [], [], []

    # 数据标准化 >> scaled_data: [values, stations]
    scaler = MinMaxScaler(feature_range=(0, 1))
    # scaler = StandardScaler()
    # scaled_data = scaler.fit_transform(np.transpose(data))
    # scaled_data = np.transpose(scaled_data)     # [values, stations] >> [stations, values]

    # 随机打乱数据集的stations维度
    # random.shuffle(scaled_data)
    data = np.array(data)
    random.shuffle(data)


    # 遍历每一个气象站点数据
    for row_idx in range(len(data)):
        # 获取当前的站点时序数据
        row_data = data[row_idx, :]

        # 计算有效数据长度（找到最后一个非零元素的索引）
        # nonzero_indices = np.where(row_data != 0)
        nonzero_indices = np.where(row_data != 0)       # [len, ]
        nonzero_indices = np.squeeze(nonzero_indices)   # [len]
        if len(nonzero_indices) == 0:
            continue    # 跳过全零的行
        valid_len = nonzero_indices[-1] + 1 # 有效数据长度

        # 检查有效数据是否足够生成窗口：
        if valid_len < window_size:
            continue

        # 生成滑动窗口
        # 逐窗口生成，stride = window_size
        num_windows = valid_len // window_size
        start_idx = 0
        for i in range(num_windows):
            end_idx = start_idx + window_size
            window = row_data[start_idx:end_idx].reshape(1, -1)

            # if window.shape == (1, window_size):
            #     window_data.append(window)

            # 训练数据归一化
            scaled_window = scaler.fit_transform(np.transpose(window))
            scaled_window = np.transpose(scaled_window)

            window_data.append(scaled_window)

            start_idx = end_idx + 1

        # 逐步生成， stride = window_size
        # num_windows = valid_len - window_size + 1
        # for start_idx in range(num_windows+1):
        #     # 滑动窗尽口截取数据
        #     end_idx = start_idx + window_size
        #     window = row_data[start_idx:end_idx].reshape(1, -1)    # [140, ] >> [1, 140]
        #
        #     # # 训练数据归一化
        #     # scaled_window = scaler.fit_transform(np.transpose(window))
        #     # scaled_window = np.transpose(scaled_window)
        #     #
        #     # window_data.append(scaled_window)
        #     if window.shape == (1, window_size):
        #         window_data.append(window)

    window_data = np.array(window_data).squeeze(1)     # list to array, [rows, 1, len] >>[rows, len]
    xs_train = np.array(window_data[:train_size, :train_len])
    ys_train = np.array(window_data[:train_size, train_len:])
    xs_test = np.array(window_data[train_size:, :train_len])
    ys_test = np.array(window_data[train_size:, train_len:])

    # 测试时的decoder输入为0向量
    # dec_zeros = torch.zeros(ys_test.shape[0], ys_test.shape[1], dtype=torch.float32).squeeze(-1)

    # 测试数据集
    # train_set_path = r'./data/train_set.xlsx'
    # with pd.ExcelWriter(train_set_path) as writer:
    #     pd.DataFrame(xs_train).to_excel(writer, sheet_name='xs_train', index=False)
    #     pd.DataFrame(xs_test).to_excel(writer, sheet_name='xs_test', index=False)
    #     pd.DataFrame(ys_train).to_excel(writer, sheet_name='ys_train', index=False)
    #     pd.DataFrame(ys_test).to_excel(writer, sheet_name='ys_test', index=False)

    train_dataset = MyDataset(enc_in=xs_train, enc_out=ys_train)
    test_dataset = MyDataset(enc_in=xs_test, enc_out=ys_test)

    return train_dataset, test_dataset

def MakeTestDataset(data, train_len, val_len):
    '''
    将test数据集按照滑动窗口切割为130长度的数据作为encoder的输入，decoder的输入和输出都为0向量
    :param data: 测试集，row代表站点，col代表时间
    :return: Tensor dataset
    '''
    window_size = train_len
    dec_size = val_len

    predict_enc = []
    predict_dec = []
    start_idxs = []

    # 数据标准化 >> scaled_data: [values, stations]
    # scaler = MinMaxScaler(feature_range=(0, 1))
    # scaled_data = scaler.fit_transform(np.transpose(data))
    # data = np.transpose(scaled_data)     # [values, stations] >> [stations, values]

    for row_index in range(len(data)):
        # 获取当前站点数据
        row_data = data.iloc[row_index, :].values
        # row_data = data[row_index, :]

        # 计算有效数据长度
        nonzero_indices = np.where(row_data != 0)       # [len, ]
        nonzero_indices = np.squeeze(nonzero_indices)   # [len]
        if len(nonzero_indices) == 0:
            continue
        valid_idx = nonzero_indices[-1]
        valid_len = valid_idx + 1
        if valid_len < window_size:
            continue
        # 存储有效数据的倒数第130位的index
        start_idxs.append(valid_idx - train_len)

        # 取最后130位数据作为encoder的输入
        enc_data = row_data[valid_idx - train_len:valid_idx]
        dec_data = np.zeros(dec_size, dtype=np.float32)
        # dec_data = torch.zeros(dec_size, dtype=torch.float32)    # dec_in size:[10]
        predict_enc.append(enc_data)
        predict_dec.append(dec_data)

    predict_enc = np.array(predict_enc).astype(float)
    predict_dec = np.array(predict_dec).astype(float)
    dec_dataset = MyDataset(enc_in=predict_enc, enc_out=predict_dec)

    # 测试训练集
    # test_set_path = r'./data/test_set.xlsx'
    # with pd.ExcelWriter(test_set_path) as writer:
    #     pd.DataFrame(predict_enc).to_excel(writer, sheet_name='predict_enc')
    #     pd.DataFrame(predict_dec).to_excel(writer, sheet_name='predict_dec')

    return dec_dataset, start_idxs

def train(model, data_loader, optimizer, criterion):
    model.train()
    epoch_train_loss = 0
    # with torch.autograd.detect_anomaly():
    for batch_idx, (enc_in, enc_out) in enumerate(data_loader):
        # enc_in: [batch, len, 1]  enc_out:[batch, len, 1]
        enc_in, enc_out = enc_in.to(device), enc_out.to(device)

        # # 手动归一化：
        # mean_enc_in = torch.mean(enc_in, dim=0, keepdim=True)
        # std_enc_in = torch.std(enc_in, dim=0, keepdim=True)
        # norm_enc_in = (enc_in-mean_enc_in) / std_enc_in
        #
        # mean_enc_out = torch.mean(enc_out, dim=0, keepdim=True)
        # std_enc_out = torch.std(enc_out, dim=0, keepdim=True)
        # norm_enc_out = (enc_out - mean_enc_out) / std_enc_out

        optimizer.zero_grad()
        output = model(enc_in)     # output: [batch, len, 1]


        loss = criterion(output, enc_out)

        loss.backward(retain_graph=False)
        optimizer.step()
        epoch_train_loss += loss.item() * enc_in.size(0)
    epoch_train_loss /= len(data_loader)
    return epoch_train_loss

def val(model, data_loader, criterion):
    model.eval()
    epoch_test_loss = 0
    with torch.no_grad():
        for batch_idx, (enc_in, enc_out) in enumerate(data_loader):
            # enc_in: [batch, len, 1]   dec_in/out: [batch, len, 1]
            enc_in, enc_out = enc_in.to(device), enc_out.to(device)

            # # 手动归一化：
            # mean_enc_in = torch.mean(enc_in, dim=0, keepdim=True)
            # std_enc_in = torch.std(enc_in, dim=0, keepdim=True)
            # norm_enc_in = (enc_in - mean_enc_in) / std_enc_in
            #
            # mean_enc_out = torch.mean(enc_out, dim=0, keepdim=True)
            # std_enc_out = torch.std(enc_out, dim=0, keepdim=True)
            # norm_enc_out = (enc_out - mean_enc_out) / std_enc_out

            # predict
            out_put = model(enc_in)     # out_put: [batch, len]

            # compute loss
            loss = criterion(out_put, enc_out)
            epoch_test_loss += loss.item() * enc_in.size(0)

    epoch_test_loss /= len(data_loader)
    return epoch_test_loss

def test(model, data_loader, train_len, pre_len):   # 测试集对130个encoder数据进行预测，直到预测长度达到260
    model.eval()
    with (torch.no_grad()):
        test_prediction = []
        for batch_idx, (enc_in, enc_out) in enumerate(data_loader):
            # enc_in: [batch, len, 1]   dec_in/out: [batch, len, 1]
            enc_in = enc_in.to(device)

            batch_predictions = enc_in.detach().cpu().numpy()
            pre_times = (max_len - enc_in.size(1)) // pre_len + 1

            # 手动归一：
            # max_enc_in, _ = torch.max(enc_in, dim=1, keepdim=True)
            # min_enc_in, _ = torch.min(enc_in, dim=1, keepdim=True)
            # enc_in = (enc_in - min_enc_in) / (max_enc_in - min_enc_in)

            # mean_enc_in = torch.mean(enc_in, dim=1, keepdim=True)
            # std_enc_in = torch.std(enc_in, dim=1, keepdim=True)
            # enc_in = (enc_in - mean_enc_in) / std_enc_in

            # 以10步长预测结果为一步，总共需要预测12次
            for _ in range(pre_times):

                out_put = model(enc_in)     # out_put: [batch, len, 1]

                # 每次根据130步长预测了10个数据后，将预测的结果添加到时序中去
                enc_opt = out_put.detach().cpu().numpy()
                batch_predictions = np.concatenate((batch_predictions, enc_opt), axis=1)
                # 更新最新的130个步长作为encoder的输入
                enc_in = torch.from_numpy(batch_predictions[:, -train_len:, :]).to(device)        # enc_in: [batch, len, 1]
                # 重置decoder的输入
                # enc_out = torch.zeros(dec_in.size(0), dec_in.size(1), 1).to(device)  # dec_in: [batch, len, 1]
                # 将预测的batch结果保存到数组中
            test_prediction.append(batch_predictions)
            # 动态拼接数组
            # max_len = max(pre.shape[1] for pre in test_prediction)
            # pad_res = []
            # for arr in test_prediction:
            #     # 计算需要填充的长度
            #     padding_len = max_len - arr.shape[1]
            #     padded_array = np.pad(arr, ((0, 0), (0, padding_len)), mode='constant')
            #     pad_res.append(padded_array)
            # prediction = np.concatenate(pad_res, axis=0)
        prediction = np.concatenate(test_prediction, axis=0)

        # # 预测部分标准化
        # pre_part = prediction[:, train_len:, :]
        #
        # max_pre_part = np.max(pre_part, axis=1, keepdims=True)
        # min_pre_part = np.min(pre_part, axis=1, keepdims=True)
        # norm_pre_part = (pre_part - min_pre_part) / (max_pre_part - min_pre_part)
        #
        # # mean_pre_part = pre_part.mean(axis=1, keepdims=True)
        # # std_pre_part = pre_part.std(axis=1, keepdims=True)
        # # norm_pre_part = (pre_part - mean_pre_part) / std_pre_part
        # prediction[:, train_len:, :] = norm_pre_part
    return prediction


if __name__ == '__main__':

    train_file = r'./data/train.xlsx'
    pre_file = r'./data/test.xlsx'
    save_file = rf'./data/opt{time.strftime("%Y_%m_%d_%H_%M", time.localtime(time.time()))}.xlsx'

    src_pad_idx = 0
    trg_pad_idx = 0
    enc_input_size = 50
    dec_input_size = 50
    d_model = 512
    max_len = 253
    n_head = 8
    ffn_hidden = 256
    n_layers = 6
    dropout = 0.1
    device = torch.device('cuda')

    train_ratio = 0.9
    train_len = 200

    epochs = 50
    batch_size = 512

    # 制作数据集
    # 训练集和验证集
    # train_data = pd.read_excel(train_file, index_col='ID',nrows=100)
    train_data = pd.read_excel(train_file, index_col='ID')
    train_dataset, test_dataset = MakeData_SW(data=train_data, train_ratio=train_ratio,
                                              train_len=enc_input_size, val_len=dec_input_size)
    train_loader = torch.utils.data.DataLoader(dataset=train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = torch.utils.data.DataLoader(dataset=test_dataset, batch_size=batch_size, shuffle=True)

    # 测试集
    # test_data = pd.read_excel(pre_file, index_col='ID')
    # test_data = pd.read_excel(pre_file, index_col='ID',nrows=100)
    test_data = pd.read_excel(pre_file, index_col='ID')
    test_dataset, start_idx = MakeTestDataset(data=test_data, train_len=enc_input_size, val_len=dec_input_size)
    test_loader = torch.utils.data.DataLoader(dataset=test_dataset, batch_size=batch_size, shuffle=False)

    model = Model(train_len=enc_input_size, pre_len=dec_input_size).cuda()

    # 设置损失函数
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

    epoch_train_loss = []
    epoch_val_loss = []

    for epoch in range(epochs):
        train_loss = train(model, train_loader, optimizer, criterion)
        epoch_train_loss.append(train_loss)

        val_loss = val(model, val_loader, criterion)
        epoch_val_loss.append(val_loss)

        # if epoch % 5 == 0:
        #     print(f'Epoch: {epoch + 1}/{epochs}, train_loss: {train_loss:.4f}, val_loss: {val_loss:.4f}')
        print(f'Epoch: {epoch + 1}/{epochs}, train_loss: {train_loss:.4f}, val_loss: {val_loss:.4f}')

    with open(f'loss_{time.strftime("%Y_%m_%d_%H_%M", time.localtime(time.time()))}.txt', 'a') as f:
        f.write(str('train loss: ' + str(epoch_train_loss) + 'val loss: ' + str(epoch_val_loss) + '\n'))

    pth_path = rf'{time.strftime("%Y_%m_%d_%H_%M", time.localtime(time.time()))}.pt'
    torch.save(model.state_dict(), pth_path)

    # 加载模型并预测（调试版本）
    # model.load_state_dict(torch.load(r'E:\LSTM\model\transformer_model.pt'))

    predictions = test(model, test_loader, enc_input_size, dec_input_size)  # [batch, len, 1]
    pre_timsries = []
    for i in range(len(predictions)):
        predict_values = predictions[i].reshape(-1)  # [batch, len, 1] >> [len, ]
        # start_idx存储着有效序列倒数第130的位置序列
        before_start_timsries = test_data.iloc[i][:start_idx[i]].values
        # 将130位以前的数据和最后130以后的预测数据拼接
        new_line = np.concatenate((before_start_timsries, predict_values), axis=0)
        pre_timsries.append(new_line)

        if i % 100 == 0:
            print(f'{i} / {len(predictions)} predict')

    new_df = pd.DataFrame(pre_timsries, index=test_data.index)
    new_df.to_excel(save_file)
    print('Done')