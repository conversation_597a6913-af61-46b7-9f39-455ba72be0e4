import torch
from torch import nn


# from models.transformer.embedding import TransformerEmbedding
# from models.transformer.layer_norm import LayerNorm
# from models.transformer.multi_head_attention import MultiHeadAttention
# from models.transformer.position_wise_feed_forward import PositionwiseFeedForward

from embedding import TransformerEmbedding
from layer_norm import LayerNorm
from multi_head_attention import MultiHeadAttention
from position_wise_feed_forward import PositionwiseFeedForward

class DecoderLayer(nn.Module):
    def __init__(self, d_model, ffn_hidden, n_head, dropout):
        super(DecoderLayer, self).__init__()
        self.attention1 = MultiHeadAttention(n_head, d_model)
        self.norm1 = LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)

        self.cross_attention = MultiHeadAttention(n_head, d_model)
        self.norm2 = LayerNorm(d_model)
        self.dropout2 = nn.Dropout(dropout)

        self.ffn = PositionwiseFeedForward(d_model, ffn_hidden, dropout)
        self.norm3 = LayerNorm(d_model)
        self.dropout3 = nn.Dropout(dropout)

    def forward(self, dec, enc, src_mask, tgt_mask):
        _x = dec
        x = self.attention1(query=dec, key=dec, value=dec, mask=tgt_mask)
        x = self.dropout1(x)
        x = self.norm1(_x + x)

        _x = x
        x = self.cross_attention(query=x, key=enc, value=enc, mask=src_mask)
        x = self.dropout2(x)
        x = self.norm2(_x + x)

        _x = x
        x = self.ffn(x)
        x = self.dropout3(x)
        x = self.norm3(_x + x)

        return x

class Decoder(nn.Module):
    def __init__(self, dec_input_size,d_model, max_len, ffn_hidden, n_head, n_layer, dropout, device):
        super(Decoder, self).__init__()
        self.embedding = TransformerEmbedding(dec_input_size, d_model, max_len, dropout, device)
        self.layers = nn.ModuleList(
            [DecoderLayer(d_model, ffn_hidden, n_head, dropout) for _ in range(n_layer)]
        )
        self.fc = nn.Linear(d_model, 1)

    def forward(self, dec, enc, src_mask, tgt_mask):
        dec = self.embedding(dec, 'decoder')
        for layer in self.layers:
            dec = layer(dec, enc, src_mask, tgt_mask)

        dec = self.fc(dec)      # [batch, dec_input_size, d_model] >> [batch, dec_input_size, 1]
        dec = dec.squeeze(-1)  # [batch, dec_input_size, 1] >> [batch, dec_input_size]
        # dec_attention = F.softmax(dec, dim=-1)  # [batch, dec_input_size]
        return dec