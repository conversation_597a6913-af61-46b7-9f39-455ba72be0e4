import torch
from torch import nn
import math

class MultiHeadAttention(nn.Module):
    '''
    in: [batch_size, length, d_model]
    out: [batch_size, length, d_model]
    '''
    def __init__(self, num_heads, d_model):
        super(MultiHeadAttention, self).__init__()
        self.num_heads = num_heads
        self.d_model = d_model
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_concat = nn.Linear(d_model, d_model)
        self.softmax = nn.Softmax(dim=-1)
        self.attention = ScaledDotProductAttention()

    def forward(self, query, key, value, mask=None):
        # [batch, train_len, d_model]
        # 1. dot product with weight matrix
        q, k, v = self.w_q(query), self.w_k(key), self.w_v(value)

        # 2. split tensor by number of heads
        q, k, v = self.split(q), self.split(k), self.split(v)

        # 3. do scale dot product to compute similarity
        v, _ = self.attention(q, k, v, mask=mask)

        # 4. inverse and concat and pass to linear layer
        out = self.concat(v)
        out = self.w_concat(out)

        # 5. visualize attention map

        return out

    def split(self, tensor):
        '''
        split tensor by number of head
        :param tensor:  [batch_size, length, d_model]
        :return:  [batch_size, head, length, d_model]
        '''
        batch_size, length, d_model = tensor.size()

        # 将子向量维度/注意力头数，把单头注意力拆分为n头注意力，最后把矩阵拼接起来
        d_tensor = d_model // self.num_heads

        tensor = tensor.view(batch_size, length, self.num_heads, d_tensor).transpose(1, 2)

        return tensor

    def concat(self, tensor):
        '''
        concatenate tensor by number of head, inverse function of self.split()
        :param tensor: [batch_size, head, length, d_model]
        :return: [batch_size, length, d_model]
        '''
        batch_size, head, length, d_model = tensor.size()
        d_model = head * d_model

        tensor = tensor.transpose(1, 2).contiguous().view(batch_size, length, d_model)

        return tensor


class ScaledDotProductAttention(nn.Module):
    def __init__(self):
        super(ScaledDotProductAttention, self).__init__()
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, query, key, value, mask=None):

        batch_size, head, length, d_model = key.size()

        # 1. dot product Q and K_T to compute similarity
        # Q x KT [batch, head, length, d_model] x [batch, head, d_model, length] -> [batch, head, length, length]
        scores = torch.matmul(query, key.transpose(-1, -2)) / math.sqrt(d_model)

        # 2. apply mask (opt). mask >> [batch, head, length, length]
        if mask is not None:
            # [batch, length, length] >> [batch, head, length, length]
            mask = mask.unsqueeze(1).repeat(1, head, 1, 1)
            scores = scores.masked_fill(mask == True, -100000)

        # 3. pass them softmax to make [0, 1] range
        scores = self.softmax(scores)

        # 4. multiply with value
        # scores: [batch, head, length, length] x V[batch, head, length, d_model] -> [batch, head, length, d_model]
        v = scores.matmul(value)

        return v, scores