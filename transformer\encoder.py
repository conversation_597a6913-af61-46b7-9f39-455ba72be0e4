import torch
import torch.nn as nn

# from models.transformer.embedding import TransformerEmbedding
# from models.transformer.layer_norm import LayerNorm
# from models.transformer.multi_head_attention import MultiHeadAttention
# from models.transformer.position_wise_feed_forward import PositionwiseFeedForward
from embedding import TransformerEmbedding
from layer_norm import LayerNorm
from multi_head_attention import MultiHeadAttention
from position_wise_feed_forward import PositionwiseFeedForward

class EncoderLayer(nn.Module):
    def __init__(self, d_model, ffn_hidden, n_head, dropout=0.1):
        super(EncoderLayer, self).__init__()
        self.attention = MultiHeadAttention(n_head, d_model)
        self.norm1 = LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.ffn = PositionwiseFeedForward(d_model, ffn_hidden, dropout)
        self.norm2 = LayerNorm(d_model)
        self.dropout2 = nn.Dropout(dropout)

    def forward(self, x, mask=None):
        # 1. compute attention
        _x = x
        x = self.attention(query=x, key=x, value=x, mask=mask)

        # 2. add and norm
        x = self.dropout1(x)
        x = self.norm1(x + _x)

        # 3. positional feed forward
        _x = x
        x = self.ffn(x)

        # 4. add and norm
        x = self.dropout2(x)
        x = self.norm2(x + _x)

        return x


class Encoder(nn.Module):
    def __init__(self, input_size, max_len, d_model, ffn_hidden, n_head, n_layer, device, dropout=0.1):
        super(Encoder, self).__init__()
        self.embedding = TransformerEmbedding(input_size=input_size, d_model=d_model,
                                              max_len=max_len, dropout=dropout, device=device)
        self.layers = nn.ModuleList(
            [EncoderLayer(d_model, ffn_hidden, n_head, dropout=dropout) for _ in range(n_layer)]
        )

    def forward(self, x, mask=None):
        # 1. embedding
        x = self.embedding(x, 'encoder')

        # 2. encoder层编码
        for layer in self.layers:
            x = layer(x, mask)

        return x