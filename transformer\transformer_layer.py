import torch
import torch.nn as nn
import numpy as np
# from models.transformer.encoder import Encoder
# from models.transformer.decoder import Decoder
from encoder import Encoder
from decoder import Decoder

class Transformer(nn.Module):
    def __init__(self, src_pad_idx, trg_pad_idx, enc_input_size, dec_input_size, d_model, max_len,
                 n_head, ffn_hidden, n_layer, dropout, device):
        super(Transformer, self).__init__()
        self.encoder = Encoder(enc_input_size, max_len, d_model, ffn_hidden, n_head, n_layer, device, dropout)
        self.decoder = Decoder(dec_input_size, d_model, max_len, ffn_hidden, n_head, n_layer, dropout, device)
        # self.fc_out = nn.Linear(d_model, dec_input_size, bias=False)
        self.src_pad_idx = src_pad_idx
        self.trg_pad_idx = trg_pad_idx
        self.device = device

    def forward(self, src, trg):
        # src_mask = self.make_src_mask(src)
        # src_mask = self.get_attention_mask(src, src)
        encoder_src_mask = self.get_attn_pad_mask(src, src)
        decoder_src_mask = self.get_attn_pad_mask(trg, src)
        trg_mask = self.make_trg_mask(trg)
        enc_src = self.encoder(src, encoder_src_mask)
        output = self.decoder(trg, enc_src, decoder_src_mask, trg_mask)
        return output

    # 方案1：
    def make_src_mask(self, src):
        # src = [batch, len]
        # pad = 0, 非0的地方为True,等于0的地方为False（mask）
        src_mask = (src != self.src_pad_idx).unsqueeze(1).unsqueeze(2)  # [batch, 1, len]
        return src_mask

    def make_trg_mask(self, trg):
        trg = trg.squeeze(-1)        # seq = [batch, len, 1] >> [batch, len]
        # trg_pad_idx = 0, 非0的地方为True,等于0的地方为False（mask）
        trg_pad_mask = (trg != self.trg_pad_idx).unsqueeze(1)   # [batch, 1, len]
        trg_len = trg.shape[1]
        # trg_sub_mask = torch.tril(torch.ones(trg_len, trg_len)).type(torch.ByteTensor).to(self.device)
        trg_sub_mask = torch.tril(torch.ones(1, trg_len, trg_len), diagonal=1).type(torch.bool).to(
            self.device)    # [1, len, len]
        # 填充占位mak + 因果Mask
        trg_mask = trg_pad_mask & trg_sub_mask  # [batch, len, len]
        return trg_mask

    # 方案2：
    def get_attn_pad_mask(self, seq_q, seq_k):
        '''
        using data before embedding to make attention mask
        :param seq_q: [batch, len_q， 1]
        :param seq_k: [batch, len_k， 1]
        :return: [batch, len_q, len_k]
        '''
        seq_q = seq_q.squeeze(-1)   # [batch, len_q， 1] >> [batch, len_q]
        seq_k = seq_k.squeeze(-1)   # [batch, len_k， 1] >> [batch, len_k]

        batch_size, len_q = seq_q.size()
        batch_size, len_k = seq_k.size()

        # 补零填充的地方为True
        padding_attn_mask = seq_k.data.eq(0).unsqueeze(1)
        return padding_attn_mask.expand(batch_size, len_q, len_k)

    def get_attn_subsequence_mask(self, seq):  # seq: [batch_size, tgt_len]
        seq = seq.squeeze(-1)
        # 该方案还需要取或，为了方便用的方案1的第二步
        attn_shape = [seq.size(0), seq.size(1), seq.size(1)]
        subsequence_mask = np.triu(np.ones(attn_shape), k=1)  # 生成上三角矩阵,[batch_size, tgt_len, tgt_len]
        subsequence_mask = torch.from_numpy(subsequence_mask).byte()  # [batch_size, tgt_len, tgt_len]
        return subsequence_mask
