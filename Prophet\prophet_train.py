import pandas as pd
from prophet import Prophet
import logging
import os  # 用于检查文件是否存在

# 抑制 Prophet 和 CmdStanPy 的信息性消息，使输出更简洁
logging.getLogger('prophet').setLevel(logging.ERROR)
logging.getLogger('cmdstanpy').setLevel(logging.ERROR)

# --- 配置参数 ---
base_data_path = r'E:/LSTM/data'  # 存放年份 XLSX 文件的基础路径
# output_file_name = r'E:/LSTM/data/Combined_Forecasts_2015-2022.xlsx'  # 最终输出文件名
output_dir = r'E:\LSTM\TransLSTM\models\RF_LSTM\data'
start_year = 2015
end_year = 2016

prediction_periods_all = 260  # 预测的期数
frequency = '16D'  # 预测的频率 (16天)

# 用于存储所有序列的组合数据 (历史数据 + 预测数据)
# 字典的键是唯一的序列名 (例如 "SeriesA_2015")，值是包含日期索引和对应值的 Pandas Series
all_series_combined_data = {}

# --- 遍历指定年份范围 ---
for year in range(start_year, end_year + 1):
    file_name = f'{year}.xlsx'
    file_path = os.path.join(base_data_path, file_name)
    output_path = os.path.join(output_dir, f'Prophet_{year}.xlsx')

    print(f"\n--- 正在处理年份: {year} ---")
    print(f"查找文件: {file_path}")

    if not os.path.exists(file_path):
        print(f"  警告: 文件 {file_path} 未找到。跳过此年份。")
        continue

    # --- 1. 加载当年数据 ---
    try:
        # 假设 Excel 文件的第一列是序列的名称/ID，并将其作为索引
        # 后续的列名是时间戳
        df_excel_raw = pd.read_excel(file_path, index_col=0)
        if df_excel_raw.empty:
            print(f"  警告: 文件 {file_path} 为空。跳过此年份。")
            continue
    except Exception as e:
        print(f"  错误: 加载 Excel 文件 {file_path} 失败: {e}。跳过此年份。")
        continue

    # --- 2. 转换列标题为时间戳 ---
    try:
        timestamps_from_cols = pd.to_datetime(df_excel_raw.columns)
    except Exception as e:
        print(f"  错误: 无法将文件 {file_path} 中的列标题转换为日期时间格式: {e}。跳过此年份。")
        continue

    print(f"  在文件 {file_path} 中找到 {len(df_excel_raw)} 个时间序列。")


    # 预测长度调整
    prediction_periods = prediction_periods_all - len(df_excel_raw.columns)  # 预测的期数

    # --- 3. 为当前文件中的每个时间序列（每一行）进行预测 ---
    for series_name, row_data in df_excel_raw.iterrows():
        # 为每个序列创建唯一名称，以防不同年份文件中有同名序列
        unique_series_name = f"{str(series_name)}_{year}"
        print(f"    正在处理序列: {unique_series_name}")

        # 准备 Prophet 所需的数据格式
        prophet_df = pd.DataFrame({
            'ds': timestamps_from_cols,
            'y': row_data.values
        })
        prophet_df.dropna(subset=['y'], inplace=True)  # 移除 'y' 列中的 NaN 值

        if prophet_df.empty or len(prophet_df) < 2:  # Prophet 模型至少需要2个数据点
            print(f"      序列 {unique_series_name} 在移除 NaN 值后数据不足。跳过此序列。")
            continue

        # 确保 prophet_df 按 'ds' 排序，以便正确获取 last_historical_date
        prophet_df_sorted = prophet_df.sort_values(by='ds').copy()

        # --- 4. 初始化并拟合 Prophet 模型 ---
        model = Prophet(holidays=None, changepoint_prior_scale=0.001)  # 不考虑节假日
        try:
            model.fit(prophet_df_sorted)
        except Exception as e:
            print(f"      错误: 为序列 {unique_series_name} 拟合 Prophet 模型失败: {e}。跳过此序列。")
            continue

        # --- 5. 创建用于预测的未来日期 DataFrame 并执行预测 ---
        future_df = model.make_future_dataframe(periods=prediction_periods, freq=frequency)
        try:
            forecast_df = model.predict(future_df)
        except Exception as e:
            print(f"      错误: 为序列 {unique_series_name} 生成预测失败: {e}。跳过此序列。")
            continue

        # --- 6. 准备组合数据：历史部分用实际值，未来部分用预测值 (yhat) ---

        # 历史数据部分 (原始的 'y' 值)
        historical_series = pd.Series(data=prophet_df_sorted['y'].values, index=prophet_df_sorted['ds'])

        # 预测数据部分 (只取未来的 'yhat' 值)
        last_historical_date = historical_series.index.max()

        # 筛选出 forecast_df 中日期在 last_historical_date 之后的部分
        future_predictions_only = forecast_df[forecast_df['ds'] > last_historical_date][['ds', 'yhat']]

        if future_predictions_only.empty and prediction_periods > 0:
            print(f"      注意: 序列 {unique_series_name} 未能生成严格在最后一个历史日期之后的未来预测点。")

        forecast_series = pd.Series(data=future_predictions_only['yhat'].values, index=future_predictions_only['ds'])

        # 合并历史数据和未来预测数据
        # pd.concat 会基于索引（日期）正确合并
        combined_series_data = pd.concat([historical_series, forecast_series])
        combined_series_data.name = unique_series_name  # 给 Series 命名，方便后续构建 DataFrame

        all_series_combined_data[str(series_name)] = combined_series_data
        print(f"      序列 {unique_series_name} 处理完毕，数据已合并。")

    # --- 7. 将所有处理过的序列数据整合到一个 DataFrame 中并保存 ---
    if not all_series_combined_data:
        print("\n未能处理任何数据或生成任何预测。不会创建输出文件。")
    else:
        print("\n--- 正在整合所有已处理序列的数据 ---")
        # 从 all_series_combined_data 字典创建 DataFrame
        # 字典的键将成为最终 DataFrame 的行索引（转置后）
        # 字典的值 (Pandas Series) 的索引将成为列索引（转置后）
        # Pandas 会自动对齐所有序列的日期，并在缺失处填充 NaN
        final_output_df = pd.DataFrame(all_series_combined_data)

        if final_output_df.empty:
            print("  最终生成的 DataFrame 为空。不会创建输出文件。")
        else:
            # 转置 DataFrame，使得行为序列名，列为时间戳
            final_output_df = final_output_df.T

            # 按时间顺序对列（时间戳）进行排序
            final_output_df = final_output_df.reindex(sorted(final_output_df.columns), axis=1)

            try:
                print(f"  正在将合并后的预测数据保存到: {output_path}")
                final_output_df.to_excel(output_path, sheet_name='CombinedForecasts')
                print(f"  成功将合并数据保存到 '{output_path}'。")
            except Exception as e:
                print(f"  错误: 保存最终合并数据到 Excel 失败: {e}")

    print("\n--- 脚本执行完毕 ---")